import React, { useEffect } from "react";
import { useUIStore } from "@/stores/uiStore";
import { UploadedFileCard } from "@/components/ui-components/UploadFileCard";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import { Upload } from "lucide-react";

const DashboardTest: React.FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const actions = [
		{
			label: "Add New",
			onClick: () => console.log("Add new clicked"),
			variant: "primary" as const,
		},
		{
			label: "Import",
			onClick: () => console.log("Import clicked"),
			variant: "outline" as const,
		},
	];

	const handleUpload = () => {
		console.log("Upload clicked");
	};

	useEffect(() => {
		const breadcrumbs = [
			{
				label: "Home",
				href: "/",
			},
			{
				label: "Dashboard",
				isCurrentPage: true,
			},
		];

		setBreadcrumbs(breadcrumbs);

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	return (
		<div>
			DashboardTest
			<EmptyContent
				title="No databases added"
				description="Read and write directly to databases and stores from your projects."
				actions={actions}
			/>
			<EmptyContent
				title="No files uploaded"
				description="Upload files to get started with your project."
				icon={<Upload className="h-7 w-7 text-gray-700" />}
				actions={[
					{
						label: "Upload Files",
						onClick: handleUpload,
						variant: "primary",
					},
				]}
			/>
		</div>
	);
};

export default DashboardTest;
