import { createBrowserRouter } from "react-router";
import { MainLayout } from "../layouts/MainLayout";
import Dashboard from "../pages/Dashboard/Dashboard";
import DashboardTest from "../pages/Dashboard/DashboardTest";
import { Locations } from "@/pages/WorkPlace";
import { ManagementTabs } from "@/pages/WorkPlace/ManagementTabs";

export const router = createBrowserRouter([
	{
		path: "/",
		Component: MainLayout,
		children: [
			// Home/landing page
			{
				index: true,
				Component: DashboardTest,
			},
			// Dashboard section
			{
				path: "dashboard",
				// Component: MainLayout, // Optional dashboard-specific layout
				children: [
					{
						index: true,
						Component: Dashboard,
					},
					{
						path: "test",
						Component: DashboardTest,
					},
					{
						path: "workplace",
						// Component: Workplace,
						children: [
							{
								index: true,
								// Component: ManagementTabs, // Main workplace page with tabs
								Component: Locations, // Main workplace page with tabs
							},
							{
								path: "locations",
								Component: Locations,
							},
						],
					},
					// Future dashboard routes
					// {
					//   path: "analytics",
					//   Component: Analytics,
					//   loader: async ({ request }) => {
					//     const user = await getCurrentUser();
					//     if (!user.permissions.includes('analytics.read')) {
					//       throw new Response("Unauthorized", { status: 401 });
					//     }
					//     return { analytics: await getAnalytics() };
					//   },
					// },
				],
			},
		],
	},
]);

// export const router = createBrowserRouter([
// 	{
// 		path: "/",
// 		Component: MainLayout,
// 		children: [
// 			// Index route - renders at "/"
// 			{
// 				index: true,
// 				Component: Dashboard,
// 			},
// 			// Dashboard routes
// 			{
// 				path: "dashboard",
// 				Component: Dashboard,
// 			},
// 			{
// 				path: "dashboard/test",
// 				Component: DashboardTest,
// 			},
// 			// Future routes
// 			// {
// 			//   path: "customers",
// 			//   Component: Customers,
// 			//   loader: async ({ request }) => {
// 			//     const user = await getCurrentUser();
// 			//     if (!user.permissions.includes('customers.read')) {
// 			//       throw new Response("Unauthorized", { status: 401 });
// 			//     }
// 			//     return { customers: await getCustomers() };
// 			//   },
// 			// },
// 		],
// 	},
// ]);
